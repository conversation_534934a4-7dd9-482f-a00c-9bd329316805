<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organization Profile - Zero Hunger</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <style>
      .con {
        display: flex;
        align-items: center;
      }
      * {
        margin: 0%;
        padding: 0%;
        box-sizing: border-box;
      }
      .pic {
        width: 300px;
        height: 300px;
        border-radius: 150px;
        margin-bottom: 20px;
        border-style: solid;
        border-width: 3px;
        border-color: white;
      }
      .container {
        display: flex;
        margin-bottom: 30px;
      }
      .img {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-top: -120px;
        margin-right: 80px;
        margin-bottom: 20px;
      }
      .form {
        display: flex;
        flex-direction: column;
        margin-top: 30px;
        margin-bottom: 20px;
      }
      .form p {
        font-size: larger;
      }
      #address {
        padding: 10px;
        border-radius: 20px;
        width: 900px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
      }
      .img button {
        width: 300px;
        height: 40px;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: medium;
        border-width: 1px;
        border-color: gray;
      }
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
      }
      .btn span {
        margin-left: 15px;
      }
      .container1 {
        background-image: url("https://images.pling.com/img/00/00/63/36/11/1777559/blue-background-wave-glare-abstract-hd-wallpaper-wallpaper-1920x1080-10wallpaper.com.jpg");
        background-repeat: no-repeat;
        background-size: cover;
        height: 280px;
      }
      .count {
        height: 190px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
        padding: 20px;
        border-radius: 20px;
        width: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-bottom: 20px;
      }
      .count p {
        font-weight: 1000;
        font-family: Arial, Helvetica, sans-serif;
        margin: 0px;
        font-size: 80px;
        color: rgb(0, 189, 0);
      }

      /* Subscription Modal Styles */
      .subscription-card {
        border: 1px solid #ddd;
        border-radius: 10px;
        padding: 20px;
        margin: 10px;
        text-align: center;
        transition: transform 0.3s ease;
        background-color: white;
      }

      .subscription-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .subscription-card.premium {
        border: 2px solid #666699;
        background-color: #f8f9fa;
      }

      .subscription-card h3 {
        color: #333;
        margin-bottom: 15px;
      }

      .price {
        font-size: 24px;
        font-weight: bold;
        color: #666699;
        margin-bottom: 20px;
      }

      .features {
        list-style: none;
        padding: 0;
        margin-bottom: 20px;
      }

      .features li {
        margin: 10px 0;
        color: #666;
      }

      .features li:before {
        content: "✓";
        color: #666699;
        margin-right: 8px;
      }

      .subscribe-btn {
        background-color: #666699;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        transition: background-color 0.3s ease;
        width: 100%;
      }

      .subscribe-btn:hover {
        background-color: #555588;
      }

      /* Toast Notification */
      .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1056;
      }

      .custom-toast {
        background-color: #666699;
        color: white;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 10px;
        display: none;
      }

      /* Responsive Adjustments */
      @media (max-width: 992px) {
        .container {
          flex-direction: column;
          align-items: center;
        }
        
        .img {
          margin-right: 0;
        }
        
        #address {
          width: 100%;
          max-width: 900px;
        }
        
        .form {
          align-items: center;
          text-align: center;
        }
      }
    </style>
  </head>
  <body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-dark bg-dark fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">ZERO HUNGER</a>
        <div class="con">
          <h3
            class="navbar-toggler"
            data-bs-toggle="offcanvas"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
            style="border: none"
          >
            <%= org_data.organization_name %>
          </h3>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasDarkNavbar"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
          >
            <span class="navbar-toggler-icon"></span>
          </button>
        </div>

        <div
          class="offcanvas offcanvas-end text-bg-dark"
          tabindex="-1"
          id="offcanvasDarkNavbar"
          aria-labelledby="offcanvasDarkNavbarLabel"
        >
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasDarkNavbarLabel">MENU</h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
            ></button>
          </div>
          <div class="offcanvas-body">
            <ul class="navbar-nav justify-content-end flex-grow-1 pe-3">
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="/org_home">Home</a>
              </li>
              <li class="nav-item">
                <a
                  class="nav-link active"
                  aria-current="page"
                  href="#"
                  style="background-color: #666699; padding-left: 20px; border-radius: 5px;"
                >Profile</a>
              </li>
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="/org_history">History</a>
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/logout">LogOut</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>

    <!-- Header Background -->
    <div class="container1"></div>

    <!-- Main Content -->
    <div class="container">
      <div class="img">
        <img
          src="https://cdn1.iconfinder.com/data/icons/trade-cool-vector-1/128/14-512.png"
          alt="Profile"
          class="pic"
        />
        <div class="btn">
          <button onclick="showEditProfile()">
            Edit Profile<span class="material-symbols-outlined">edit</span>
          </button>
        </div>
        <div class="count">
          <h3>No. Donations Get:</h3>
          <p><%= no_donations %></p>
        </div>
      </div>
      <div class="form">
        <h1 id="name"><%= org_data.organization_name %></h1>
        <span>(Organization)</span>
        <span>ID: <%= org_data.organization_id %></span>
        <p id="contact">+91 <%= org_data.ph_no %></p>
        <p id="email"><%= org_data.email %></p>
        <div id="address">
          <p><%= org_data.city %>/<%= org_data.street %></p>
          <p><%= org_data.dist %></p>
          <p><%= org_data.state %>-<%= org_data.pincode %></p>
        </div>
      </div>
    </div>

    <!-- Impact Stats -->
    <div class="container">
      <div class="count">
        <div style="background-image: url('https://banner2.cleanpng.com/20180821/rpb/5e5e8c7f30d5a14546693c87d8d79ff5.webp'); background-size: cover; width: 95px; height: 95px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <img src="https://banner2.cleanpng.com/20180821/rpb/5e5e8c7f30d5a14546693c87d8d79ff5.webp" alt="Impact" style="width: 95px; height: auto; border-radius: 50%;">
        </div>          
        <p><%= no_donations*5 %></p>
      </div>
    </div>

    <!-- Subscription Modal -->
    <div class="modal fade" id="subscriptionModal" tabindex="-1" aria-labelledby="subscriptionModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="subscriptionModalLabel">Upgrade Your Account</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-6">
                <div class="subscription-card">
                  <h3>Basic Plan</h3>
                  <div class="price">$9.99/month</div>
                  <ul class="features">
                    <li>Up to 50 donations per month</li>
                    <li>Basic analytics</li>
                    <li>Email support</li>
                    <li>Standard reporting</li>
                  </ul>
                  <button class="subscribe-btn" onclick="handleSubscription('basic')">Subscribe Now</button>
                </div>
              </div>
              <div class="col-md-6">
                <div class="subscription-card premium">
                  <h3>Premium Plan</h3>
                  <div class="price">$19.99/month</div>
                  <ul class="features">
                    <li>Unlimited donations</li>
                    <li>Advanced analytics</li>
                    <li>24/7 Priority support</li>
                    <li>Custom reporting</li>
                    <li>API access</li>
                  </ul>
                  <button class="subscribe-btn" onclick="handleSubscription('premium')">Subscribe Now</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast Notification Container -->
    <div class="toast-container"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Check donation count and show subscription modal if needed
        const countElement = document.querySelector('.count p');
        if (countElement) {
          const donationCount = parseInt(countElement.textContent);
          if (donationCount > 5) {
            const modal = new bootstrap.Modal(document.getElementById('subscriptionModal'));
            modal.show();
          }
        }
      });

      function showToast(message, duration = 3000) {
        const toastContainer = document.querySelector('.toast-container');
        const toast = document.createElement('div');
        toast.className = 'custom-toast';
        toast.textContent = message;
        toast.style.display = 'block';
        toastContainer.appendChild(toast);

        setTimeout(() => {
          toast.style.display = 'none';
          setTimeout(() => {
            toastContainer.removeChild(toast);
          }, 300);
        }, duration);
      }

      function handleSubscription(plan) {
        // Here you would typically make an API call to your backend
        // For now, we'll just show a success message
        const planName = plan.charAt(0).toUpperCase() + plan.slice(1);
        showToast(`Thank you for choosing the ${planName} plan! Processing your subscription...`);
        
        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('subscriptionModal'));
        modal.hide();

        // Simulate API call
        setTimeout(() => {
          showToast('Subscription activated successfully!');
        }, 2000);
      }

      function showEditProfile() {
        // Implement your edit profile logic here
        showToast('Edit profile feature coming soon!');
      }
    </script>
  </body>
</html>