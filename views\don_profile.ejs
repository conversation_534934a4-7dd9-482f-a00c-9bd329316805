<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@24,400,0,0"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>
    <style>
      .con {
        display: flex;
        align-items: center;
      }
      * {
        margin: 0%;
        padding: 0%;
        box-sizing: border-box;
      }
      .pic {
        width: 300px;
        height: 300px;
        border-radius: 150px;
        margin-bottom: 20px;
        border-style: solid;
        border-width: 3px;
        border-color: white;
      }
      .container {
        display: flex;
        margin-bottom: 20px; /* Add space between containers */
      }
      .img {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin-top: -120px;
        margin-right: 80px;
      }
      .form {
        display: flex;
        flex-direction: column;
        margin-top: 30px;
      }
      .form p {
        font-size: larger;
      }
      #address {
        padding: 10px;
        border-radius: 20px;
        width: 900px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
      }
      .img button {
        width: 300px;
        height: 40px;
        border-radius: 5px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: medium;
        border-width: 1px;
        border-color: gray;
      }
      .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
      }
      .btn span {
        margin-left: 15px;
      }
      .container1 {
        background-image: url("https://images.pling.com/img/00/00/63/36/11/1777559/blue-background-wave-glare-abstract-hd-wallpaper-wallpaper-1920x1080-10wallpaper.com.jpg");
        background-repeat: no-repeat;
        background-size: cover;
        height: 280px;
        margin-bottom: 20px; /* Add space after this container */
      }
      .count {
        height: 190px;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
        padding: 20px;
        border-radius: 20px;
        width: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .count p {
        font-weight: 1000;
        font-family: Arial, Helvetica, sans-serif;
        margin: 0px;
        font-size: 80px;
        color: rgb(0, 189, 0);
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-dark bg-dark fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">ZERO HUNGER</a>
        <div class="con">
          <h3
            class="navbar-toggler"
            data-bs-toggle="offcanvas"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
            style="border: none"
          >
            <%= don_data.Donor_name %>
          </h3>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasDarkNavbar"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
          >
            <span class="navbar-toggler-icon"></span>
          </button>
        </div>

        <div
          class="offcanvas offcanvas-end text-bg-dark"
          tabindex="-1"
          id="offcanvasDarkNavbar"
          aria-labelledby="offcanvasDarkNavbarLabel"
        >
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasDarkNavbarLabel">MENU</h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
            ></button>
          </div>
          <div class="offcanvas-body">
            <ul class="navbar-nav justify-content-end flex-grow-1 pe-3">
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="/don_home"
                  >Home</a
                >
              </li>
              <li class="nav-item">
                <a
                  class="nav-link active"
                  aria-current="page"
                  href="#"
                  style="
                    background-color: #666699;
                    padding-left: 20px;
                    border-radius: 5px;
                  "
                  >Profile</a
                >
              </li>
              <li class="nav-item">
                <a
                  class="nav-link active"
                  aria-current="page"
                  href="/don_history"
                  >History</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/logout">LogOut</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
    <div class="container1"></div>
    <div class="container">
      <div class="img">
        <img
          src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTZ0FpBg5Myb9CQ-bQpFou9BY9JXoRG6208_Q&usqp=CAU"
          alt="Profile Picture"
          class="pic"
        />
        <div class="btn">
          <button>
            Edit Profile<span class="material-symbols-outlined"> edit </span>
          </button>
        </div>
        <div class="count">
          <h3>No. of Donations:</h3>
          <p><%= no_donations %></p>
        </div>
      </div>
      <div class="form">
        <h1 id="name"><%= don_data.Donor_name %></h1>
        <span>( Donor )</span>
        <p id="contact">+91 <%= don_data.ph_no %></p>
        <p id="email"><%= don_data.email %></p>
        <div id="address">
          <p><%= don_data.city %>,<%= don_data.street %></p>
          <p><%= don_data.dist %></p>
          <p><%= don_data.state %>-<%= don_data.pincode %></p>
        </div>
      </div>
    </div>
    <div class="container">
      <div class="count">
        <div style="background-image: url('https://banner2.cleanpng.com/20180821/rpb/5e5e8c7f30d5a14546693c87d8d79ff5.webp'); background-size: cover; width: 95px; height: 95px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
          <img src="https://banner2.cleanpng.com/20180821/rpb/5e5e8c7f30d5a14546693c87d8d79ff5.webp" alt="" style="width: 95px; height: auto; border-radius: 50%;">
        </div>          
        <p><%= no_donations*5 %></p>
      </div>
    </div>
  </body>
</html>
