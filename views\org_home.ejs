<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Organization Home</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"
      integrity="sha384-C6RzsynM9kWDrMNeT87bh95OGNyZPhcTNXj1NW7RuBCsyN/o0jlpcV8Qyq46cDfL"
      crossorigin="anonymous"
    ></script>
    <style>
      @import url("https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz@1,9..40&family=Playfair+Display:wght@500&family=Recursive:wght@900&display=swap");
      body {
        height: 100%;
        background-image: url("https://static.vecteezy.com/system/resources/previews/003/359/042/non_2x/modern-abstract-flat-geometric-liquid-shapes-background-free-vector.jpg");
        background-attachment: fixed;
        background-repeat: no-repeat;
        background-size: cover;
      }
      .con {
        display: flex;
        align-items: center;
      }
      .firstcon {
        margin-top: 3%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 600px;
      }
      .head {
        width: 40%;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }
      .maincon {
        height: 61vh;
        display: flex;
        justify-content: center;
        border-radius: 10px;
        margin: 5%;
        padding: 4% 0%;
        background-color: #f8f8f8;
        box-shadow: 1px 1px 10px gray;
      }
      .slogan {
        margin-top: 5%;
        width: 60%;
      }
      .h1,
      .h2 {
        font-size: 300%;
        font-family: "DM Sans", sans-serif;
        font-family: "Playfair Display", serif;
        font-family: "Recursive", sans-serif;
      }
      .one-items {
        height: auto;
        margin-top: 1%;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
      }
      .one-item-one {
        box-shadow: 1.3px 1.3px 3px gray;
        padding: 0.5% 5%;
        margin-right: 2%;
        border-radius: 5px;
        display: flex;
        align-items: center;
        flex-direction: column;
      }
      .one-btn {
        height: 40px;
        width: 130px;
        color: white;
        background-color: #2a2525db;
        border-radius: 5px;
        border: none;
      }
      .one-btn:hover {
        background-color: rgba(255, 255, 255, 0.25);
        color: black;
        transition: 0.3s;
        border: 2px solid #666699;
      }
      .connn {
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
        margin: 1% 5%;
        padding: 2%;
        background-color: #eaeaea;
        border-radius: 10px;
      }
      .donhead {
        margin-left: 7%;
      }
      .lastcon{
        height:55px;
        margin-top: 10px;
        margin-bottom: 10px;
      }
      .lastcon p{
        margin:0px;
      }
      .sel{
        text-align: end;
      }
      #orderid{
        background-color: transparent;
        border: 1px solid gray;
        border-radius: 5px;
        padding: 6px;
      }
      select{
        background-color: transparent;
        border-radius: 5px;
        padding: 5px;
      }
      .rehead{
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .rehead div{
        margin-right: 100px;
        height: 50px;
        padding: 10px;
        background-color: rgba(225, 225, 225, 0.418);
        border: 1px solid gray;
        display: flex;
        align-items: center;
        border-radius: 10px;
      }
      .rehead div p{
        margin: 0;
      }
    </style>
  </head>
  <body>
    <nav class="navbar navbar-dark bg-dark fixed-top">
      <div class="container-fluid">
        <a class="navbar-brand" href="#">ZERO HUNGER</a>
        <div class="con">
          <h3
            class="navbar-toggler"
            data-bs-toggle="offcanvas"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
            style="border: none"
          >
            <%= name %>
          </h3>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="offcanvas"
            data-bs-target="#offcanvasDarkNavbar"
            aria-controls="offcanvasDarkNavbar"
            aria-label="Toggle navigation"
          >
            <span class="navbar-toggler-icon"></span>
          </button>
        </div>

        <div
          class="offcanvas offcanvas-end text-bg-dark"
          tabindex="-1"
          id="offcanvasDarkNavbar"
          aria-labelledby="offcanvasDarkNavbarLabel"
        >
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="offcanvasDarkNavbarLabel">MENU</h5>
            <button
              type="button"
              class="btn-close btn-close-white"
              data-bs-dismiss="offcanvas"
              aria-label="Close"
            ></button>
          </div>
          <div class="offcanvas-body">
            <ul class="navbar-nav justify-content-end flex-grow-1 pe-3">
              <li class="nav-item">
                <a
                  class="nav-link active"
                  aria-current="page"
                  href="#"
                  style="
                    background-color: #666699;
                    padding-left: 20px;
                    border-radius: 5px;
                  "
                  >Home</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="/org_profile"
                  >Profile</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link active" aria-current="page" href="/org_history"
                  >History</a
                >
              </li>
              <li class="nav-item">
                <a class="nav-link" href="/logout">LogOut</a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
    <div class="firstcon">
      <div class="maincon">
        <div class="head">
          <h1 class="h1">FOOD</h1>
          <h1 class="h2">DONATION</h1>
          <p class="slogan">
            "Zero Hunger is a simple act of kindness that can nourish both the
            body and soul, bridging the gap between abundance and hunger in our
            communities."
          </p>
        </div>

        <img
          src="https://i.ibb.co/BjmNphz/download.png"
          alt=""
          height="100%"
          width="40%"
          class="rightimage"
        />
      </div>
    </div>

    <div class="bottom">
      <div class="rehead">
        <h2 class="donhead">Donation Requests:</h2>
        <div>
            <p>See Your Requests in History</p>
        </div>
      </div>
      <div class="bottomcon">
        <% for(let i = 0; i < dataArr.org_his_data.length; i++){ %>
        <div class="connn">
            <div class="con1">
                <p class="donname"><b>Donor Name:</b><%= dataArr.org_his_data[i].Donor_name %></p>
                <p class="donation"><b>Donation:</b><%= dataArr.org_his_data[i].Donation %></p>
                <p class="address"><b>Address:</b></p>
                <p class="address2">
                    <%= dataArr.org_his_data[i].Donor_address %>
                </p>
                <b>Items:</b>
            <div class="one-items">
              <% if (Array.isArray(dataArr.org_his_data[i].Items)) { %>
                <% for (let j = 0; j < dataArr.org_his_data[i].Items.length; j++) { %>
                  <div class="one-item-one">
                    <span><%= dataArr.org_his_data[i].Items[j] %></span>
                    <span><span style="color: gray;">Qty:</span><%= dataArr.org_his_data[i].EachItem_Qty[j] %></span>
                  </div>
                <% } %>
              <% } else { %>
                <div class="one-item-one">
                    <span><%= dataArr.org_his_data[i].Items %></span>
                    <span><span style="color: gray;">Qty:</span><%= dataArr.org_his_data[i].EachItem_Qty %></span>
                  </div>
              <% } %>
            </div>
            </div>
            <div class="con2">
              <p class="date"><b>Date:</b><%= dataArr.org_his_data[i].Date %></p>
              <p class="ph"><b>Phone:</b><%= dataArr.org_his_data[i].Donor_ph_no %></p>
              <p class="ph"><b>Delivery Phone:</b><%= dataArr.org_his_data[i].Donor_ph_no %></p>
            </div>
            <div class="ack">
                <div class="sel">
                    <form action="/donation_accept" method="post">
                      <div style="display: flex; flex-direction: column;">
                        <b>Email:</b>
                        <input type="text" value="<%=dataArr.org_his_data[i].Donor_email %>" name="orderemail" id="orderid" readonly >
                        <b>Order Id:</b>
                        <input type="text" value="<%=dataArr.org_his_data[i].OrderId %>" name="orderid" id="orderid" readonly >
                      </div>
                      <div class="lastcon">
                          <p>Select collection time</p>
                          <select name="time">
                              <option value="1hr">1 hr</option>
                              <option value="2hrs">2 hrs</option>
                              <option value="3hrs">3 hrs</option>
                              <option value="4hrs">4 hrs</option>
                              <option value="5hrs">5 hrs</option>
                          </select>
                      </div>
                      <button class="one-btn" class="accept">Accept</button>              
                    </form>
                </div>
            </div>
        </div>
        <% } %>
      </div>
  </body>
</html>
